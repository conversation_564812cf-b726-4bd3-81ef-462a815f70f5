<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Farm Code Challenge 2025 - Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2d5016 0%, #4a7c59 50%, #6b8e23 100%);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 3rem;
            background: linear-gradient(45deg, #90EE90, #32CD32, #228B22);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
            text-shadow: 0 0 30px rgba(144, 238, 144, 0.5);
        }

        .header p {
            font-size: 1.2rem;
            color: #b8e6b8;
        }

        .game-container {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 20px;
            margin-bottom: 30px;
        }

        .farm-grid {
            background: rgba(0, 0, 0, 0.4);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 2px;
            max-width: 400px;
            margin: 0 auto;
        }

        .cell {
            width: 40px;
            height: 40px;
            border: 1px solid #4a7c59;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .cell.soil {
            background: #8B4513;
        }

        .cell.planted {
            background: #654321;
        }

        .cell.growing {
            background: #228B22;
        }

        .cell.ready {
            background: #32CD32;
            animation: glow 2s infinite alternate;
        }

        .cell.drone {
            background: #FFD700;
            box-shadow: 0 0 15px #FFD700;
        }

        @keyframes glow {
            from { box-shadow: 0 0 5px #32CD32; }
            to { box-shadow: 0 0 20px #32CD32; }
        }

        .control-panel {
            background: rgba(0, 0, 0, 0.4);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }

        .stats {
            margin-bottom: 20px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }

        .controls {
            margin-bottom: 20px;
        }

        .btn {
            width: 100%;
            padding: 10px;
            margin-bottom: 10px;
            border: none;
            border-radius: 8px;
            background: linear-gradient(45deg, #32CD32, #228B22);
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(50, 205, 50, 0.4);
        }

        .btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .code-editor {
            background: rgba(0, 0, 0, 0.4);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            margin-top: 20px;
        }

        .code-editor h3 {
            margin-bottom: 15px;
            color: #90EE90;
        }

        .code-area {
            width: 100%;
            height: 200px;
            background: #1a1a1a;
            color: #90EE90;
            border: 1px solid #4a7c59;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            resize: vertical;
        }

        .weather {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .weather-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .leaderboard {
            background: rgba(0, 0, 0, 0.4);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            margin-top: 20px;
        }

        .leaderboard h3 {
            margin-bottom: 15px;
            color: #FFD700;
            text-align: center;
        }

        .leader-item {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            margin-bottom: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }

        .leader-item.top1 {
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: #000;
        }

        .leader-item.top2 {
            background: linear-gradient(45deg, #C0C0C0, #A0A0A0);
        }

        .leader-item.top3 {
            background: linear-gradient(45deg, #CD7F32, #B8860B);
        }

        @media (max-width: 768px) {
            .game-container {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .grid {
                grid-template-columns: repeat(6, 1fr);
            }
            
            .cell {
                width: 35px;
                height: 35px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌾 Farm Code Challenge 2025</h1>
            <p>Lập trình điều khiển drone nông nghiệp thông minh</p>
        </div>

        <div class="weather">
            <div class="weather-icon" id="weatherIcon">☀️</div>
            <div id="weatherText">Nắng - Cây trồng cần nhiều nước hơn</div>
        </div>

        <div class="game-container">
            <div class="farm-grid">
                <h3 style="text-align: center; margin-bottom: 15px; color: #90EE90;">🚁 Nông Trại Thông Minh</h3>
                <div class="grid" id="farmGrid"></div>
            </div>

            <div class="control-panel">
                <div class="stats">
                    <h3 style="color: #90EE90; margin-bottom: 15px;">📊 Thống Kê</h3>
                    <div class="stat-item">
                        <span>💰 Điểm số:</span>
                        <span id="score">0</span>
                    </div>
                    <div class="stat-item">
                        <span>🌱 Hạt giống:</span>
                        <span id="seeds">10</span>
                    </div>
                    <div class="stat-item">
                        <span>💧 Nước:</span>
                        <span id="water">20</span>
                    </div>
                    <div class="stat-item">
                        <span>🔋 Năng lượng:</span>
                        <span id="energy">100</span>
                    </div>
                    <div class="stat-item">
                        <span>🕐 Thời gian:</span>
                        <span id="time">0s</span>
                    </div>
                </div>

                <div class="controls">
                    <h3 style="color: #90EE90; margin-bottom: 15px;">🎮 Điều Khiển</h3>
                    <button class="btn" onclick="plantSeed()">🌱 Gieo Hạt</button>
                    <button class="btn" onclick="waterCrop()">💧 Tưới Nước</button>
                    <button class="btn" onclick="harvestCrop()">🌾 Thu Hoạch</button>
                    <button class="btn" onclick="moveDrone()">🚁 Di Chuyển</button>
                    <button class="btn" onclick="runAutoCode()" style="background: linear-gradient(45deg, #FFD700, #FFA500);">⚡ Chạy Code Tự Động</button>
                </div>
            </div>
        </div>

        <div class="code-editor">
            <h3>💻 Code Editor - Viết thuật toán tự động</h3>
            <textarea class="code-area" id="codeArea" placeholder="# Viết code Python để điều khiển drone
# Ví dụ:
def auto_farm():
    for i in range(8):
        for j in range(8):
            move_to(i, j)
            if can_plant():
                plant_seed()
            elif can_water():
                water_crop()
            elif can_harvest():
                harvest_crop()

auto_farm()"></textarea>
        </div>

        <div class="leaderboard">
            <h3>🏆 Bảng Xếp Hạng</h3>
            <div class="leader-item top1">
                <span>🥇 CodeMaster2025</span>
                <span>2,450 điểm</span>
            </div>
            <div class="leader-item top2">
                <span>🥈 FarmBot_Pro</span>
                <span>2,180 điểm</span>
            </div>
            <div class="leader-item top3">
                <span>🥉 DroneExpert</span>
                <span>1,920 điểm</span>
            </div>
            <div class="leader-item">
                <span>4. AutoFarmer</span>
                <span>1,650 điểm</span>
            </div>
            <div class="leader-item">
                <span>5. SmartCrop</span>
                <span>1,420 điểm</span>
            </div>
        </div>
    </div>

    <script>
        // Game state
        let gameState = {
            droneX: 0,
            droneY: 0,
            score: 0,
            seeds: 10,
            water: 20,
            energy: 100,
            time: 0,
            grid: [],
            weather: 'sunny'
        };

        // Initialize game
        function initGame() {
            const grid = document.getElementById('farmGrid');
            gameState.grid = [];
            
            for (let i = 0; i < 64; i++) {
                const cell = document.createElement('div');
                cell.className = 'cell soil';
                cell.dataset.index = i;
                cell.onclick = () => selectCell(i);
                grid.appendChild(cell);
                
                gameState.grid.push({
                    type: 'soil',
                    growth: 0,
                    watered: false
                });
            }
            
            updateDronePosition();
            updateStats();
            startGameTimer();
            changeWeather();
        }

        function selectCell(index) {
            const x = index % 8;
            const y = Math.floor(index / 8);
            gameState.droneX = x;
            gameState.droneY = y;
            updateDronePosition();
        }

        function updateDronePosition() {
            const cells = document.querySelectorAll('.cell');
            cells.forEach(cell => cell.classList.remove('drone'));
            
            const droneIndex = gameState.droneY * 8 + gameState.droneX;
            cells[droneIndex].classList.add('drone');
        }

        function plantSeed() {
            if (gameState.seeds <= 0 || gameState.energy < 5) return;
            
            const index = gameState.droneY * 8 + gameState.droneX;
            const cell = gameState.grid[index];
            
            if (cell.type === 'soil') {
                cell.type = 'planted';
                cell.growth = 1;
                gameState.seeds--;
                gameState.energy -= 5;
                updateCellDisplay(index);
                updateStats();
            }
        }

        function waterCrop() {
            if (gameState.water <= 0 || gameState.energy < 3) return;
            
            const index = gameState.droneY * 8 + gameState.droneX;
            const cell = gameState.grid[index];
            
            if (cell.type === 'planted' || cell.type === 'growing') {
                cell.watered = true;
                cell.growth += gameState.weather === 'rain' ? 2 : 1;
                gameState.water--;
                gameState.energy -= 3;
                
                if (cell.growth >= 3) {
                    cell.type = 'ready';
                }
                
                updateCellDisplay(index);
                updateStats();
            }
        }

        function harvestCrop() {
            if (gameState.energy < 8) return;
            
            const index = gameState.droneY * 8 + gameState.droneX;
            const cell = gameState.grid[index];
            
            if (cell.type === 'ready') {
                const points = gameState.weather === 'sunny' ? 50 : 30;
                gameState.score += points;
                gameState.seeds += 2;
                gameState.energy -= 8;
                
                cell.type = 'soil';
                cell.growth = 0;
                cell.watered = false;
                
                updateCellDisplay(index);
                updateStats();
            }
        }

        function moveDrone() {
            if (gameState.energy < 2) return;
            
            // Random movement for demo
            const directions = [
                {x: 0, y: -1}, {x: 1, y: 0}, {x: 0, y: 1}, {x: -1, y: 0}
            ];
            const dir = directions[Math.floor(Math.random() * 4)];
            
            const newX = Math.max(0, Math.min(7, gameState.droneX + dir.x));
            const newY = Math.max(0, Math.min(7, gameState.droneY + dir.y));
            
            gameState.droneX = newX;
            gameState.droneY = newY;
            gameState.energy -= 2;
            
            updateDronePosition();
            updateStats();
        }

        function updateCellDisplay(index) {
            const cell = document.querySelector(`[data-index="${index}"]`);
            const cellData = gameState.grid[index];
            
            cell.className = 'cell';
            
            switch(cellData.type) {
                case 'soil':
                    cell.className += ' soil';
                    cell.textContent = '';
                    break;
                case 'planted':
                    cell.className += ' planted';
                    cell.textContent = '🌱';
                    break;
                case 'growing':
                    cell.className += ' growing';
                    cell.textContent = '🌿';
                    break;
                case 'ready':
                    cell.className += ' ready';
                    cell.textContent = '🌾';
                    break;
            }
            
            if (index === gameState.droneY * 8 + gameState.droneX) {
                cell.classList.add('drone');
            }
        }

        function updateStats() {
            document.getElementById('score').textContent = gameState.score;
            document.getElementById('seeds').textContent = gameState.seeds;
            document.getElementById('water').textContent = gameState.water;
            document.getElementById('energy').textContent = gameState.energy;
        }

        function startGameTimer() {
            setInterval(() => {
                gameState.time++;
                document.getElementById('time').textContent = gameState.time + 's';
                
                // Auto grow crops
                gameState.grid.forEach((cell, index) => {
                    if (cell.type === 'planted' && cell.watered && Math.random() < 0.1) {
                        cell.type = 'growing';
                        updateCellDisplay(index);
                    } else if (cell.type === 'growing' && Math.random() < 0.05) {
                        cell.type = 'ready';
                        updateCellDisplay(index);
                    }
                });
                
                // Regenerate resources slowly
                if (gameState.time % 10 === 0) {
                    gameState.energy = Math.min(100, gameState.energy + 5);
                    gameState.water = Math.min(50, gameState.water + 1);
                    updateStats();
                }
            }, 1000);
        }

        function changeWeather() {
            const weathers = [
                {type: 'sunny', icon: '☀️', text: 'Nắng - Cây trồng cần nhiều nước hơn'},
                {type: 'rain', icon: '🌧️', text: 'Mưa - Cây phát triển nhanh hơn'},
                {type: 'storm', icon: '⛈️', text: 'Bão - Nguy hiểm cho cây trồng!'}
            ];
            
            setInterval(() => {
                const weather = weathers[Math.floor(Math.random() * weathers.length)];
                gameState.weather = weather.type;
                document.getElementById('weatherIcon').textContent = weather.icon;
                document.getElementById('weatherText').textContent = weather.text;
            }, 15000);
        }

        function runAutoCode() {
            alert('🚀 Tính năng này sẽ thực thi code Python của bạn để điều khiển drone tự động!\n\nTrong phiên bản thực tế, code sẽ được chạy trên server và điều khiển drone theo thuật toán bạn viết.');
        }

        // Initialize game when page loads
        window.onload = initGame;
    </script>
</body>
</html>
