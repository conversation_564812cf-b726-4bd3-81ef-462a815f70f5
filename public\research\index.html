<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sự kiện - Vthon</title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../assets/images/favicon.png">
    <link rel="shortcut icon" type="image/png" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="72x72" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="114x114" href="../assets/images/favicon.png">

    <link rel="stylesheet" href="../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #0f2027 0%, #203a43 25%, #2c5530 50%, #1a4c2e 75%, #0d3d1a 100%);
            color: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 255, 120, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(50, 205, 50, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(34, 139, 34, 0.05) 0%, transparent 50%),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="farmGrid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(50,205,50,0.08)" stroke-width="0.5"/><circle cx="10" cy="10" r="1" fill="rgba(144,238,144,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23farmGrid)"/></svg>');
            opacity: 0.6;
            z-index: -1;
        }

        /* Floating farm elements */
        .farm-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .farm-particle {
            position: absolute;
            font-size: 1.5rem;
            opacity: 0.3;
            animation: farmFloat 15s linear infinite;
        }

        .farm-particle:nth-child(1) { left: 10%; animation-delay: 0s; }
        .farm-particle:nth-child(2) { left: 20%; animation-delay: -3s; }
        .farm-particle:nth-child(3) { left: 30%; animation-delay: -6s; }
        .farm-particle:nth-child(4) { left: 40%; animation-delay: -9s; }
        .farm-particle:nth-child(5) { left: 50%; animation-delay: -12s; }
        .farm-particle:nth-child(6) { left: 60%; animation-delay: -2s; }
        .farm-particle:nth-child(7) { left: 70%; animation-delay: -5s; }
        .farm-particle:nth-child(8) { left: 80%; animation-delay: -8s; }
        .farm-particle:nth-child(9) { left: 90%; animation-delay: -11s; }

        @keyframes farmFloat {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 0.3;
            }
            90% {
                opacity: 0.3;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        /* Modern glassmorphism cards */
        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        /* Farm event specific styles */
        .farm-event {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 20px;
            padding: 40px 20px;
            margin-top: 60px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(50, 205, 50, 0.2);
        }

        .farm-event .event-header {
            margin-bottom: 40px;
        }

        .farm-event .event-title {
            font-size: 2.8rem;
        }

        .farm-event .gradient-text {
            background: linear-gradient(135deg, #90EE90, #32CD32, #228B22);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .farm-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .farm-info-card {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .farm-info-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.12);
            border-color: rgba(50, 205, 50, 0.3);
        }

        .farm-info-card .info-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .farm-info-card .info-header i {
            color: #32CD32;
            font-size: 1.2rem;
        }

        .farm-info-card h3 {
            color: white;
            font-size: 1.1rem;
            margin: 0;
        }

        .farm-info-card p {
            color: #b8c6db;
            line-height: 1.6;
            margin: 0;
        }

        .farm-prizes {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }

        .farm-prize-card {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 15px;
            padding: 25px 15px;
            text-align: center;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .farm-prize-card:hover {
            transform: translateY(-5px);
        }

        .farm-prize-card.top1 {
            border-color: #FFD700;
            background: rgba(255, 215, 0, 0.1);
        }

        .farm-prize-card.top2 {
            border-color: #C0C0C0;
            background: rgba(192, 192, 192, 0.1);
        }

        .farm-prize-card.top3 {
            border-color: #CD7F32;
            background: rgba(205, 127, 50, 0.1);
        }

        .page-header {
            text-align: center;
            margin: 120px 0 50px;
            position: relative;
            z-index: 2;
        }

        .page-header h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #FFD700, #FFA500, #FF8C00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 800;
            text-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
        }

        .page-header p {
            color: #b8c6db;
            font-size: 1.3rem;
            max-width: 700px;
            margin: 0 auto;
        }

        .events-container {
            padding: 20px 0 50px;
            position: relative;
            z-index: 2;
        }

    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="../classes/">Lớp Học</a></li>
                    <li><a href="../achievements/">Thành Tích</a></li>
                    <li><a href="../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="index.html" class="active">Sự kiện</a></li>
                    <li><a href="../auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Farm Particles -->
    <div class="farm-particles">
        <div class="farm-particle">🌱</div>
        <div class="farm-particle">🌾</div>
        <div class="farm-particle">🚁</div>
        <div class="farm-particle">💧</div>
        <div class="farm-particle">🌿</div>
        <div class="farm-particle">🌽</div>
        <div class="farm-particle">🥕</div>
        <div class="farm-particle">🍃</div>
        <div class="farm-particle">⚡</div>
    </div>

    <!-- Events Section -->
    <section class="code-camp-event" id="events">
        <div class="container">
            <div class="page-header" data-aos="fade-up">
                <h1>Sự kiện & Cuộc thi</h1>
                <p>Tham gia các sự kiện và cuộc thi lập trình hấp dẫn tại Vthon Academy</p>
            </div>

            <!-- Code Camp Hè 2025 Event -->
            <div class="event-header" data-aos="fade-up">
                <div class="event-badge">
                    <i class="fas fa-fire"></i>
                    <span>ĐANG DIỄN RA</span>
                </div>
                <h2 class="event-title">
                    <span class="gradient-text">Code Camp Hè 2025</span>
                    <div class="title-decoration">
                        <div class="decoration-line"></div>
                        <i class="fas fa-trophy"></i>
                        <div class="decoration-line"></div>
                    </div>
                </h2>
                <p class="event-subtitle">Cuộc thi lập trình hè dành cho học viên Python</p>
            </div>

            <div class="event-content">
                <div class="event-info" data-aos="fade-right">
                    <div class="info-card">
                        <div class="info-header">
                            <i class="fas fa-calendar-alt"></i>
                            <h3>Thời Gian Diễn Ra</h3>
                        </div>
                        <div class="date-range">
                            <div class="date-item">
                                <span class="date-label">Bắt đầu</span>
                                <span class="date-value">14/06/2025</span>
                            </div>
                            <div class="date-separator">
                                <i class="fas fa-arrow-right"></i>
                            </div>
                            <div class="date-item">
                                <span class="date-label">Kết thúc</span>
                                <span class="date-value">01/08/2025</span>
                            </div>
                        </div>
                    </div>

                    <div class="info-card">
                        <div class="info-header">
                            <i class="fas fa-users"></i>
                            <h3>Đối Tượng Tham Gia</h3>
                        </div>
                        <p>Tất cả học viên đang theo học các lớp Python tại Vthon Academy</p>
                    </div>

                    <div class="info-card">
                        <div class="info-header">
                            <i class="fas fa-chart-line"></i>
                            <h3>Cách Thức Tham Gia</h3>
                        </div>
                        <p>Điểm số trên <strong>Bảng Xếp Hạng</strong> sẽ quyết định thứ hạng cuối cùng. Hãy hoàn thành các bài tập để tích lũy điểm!</p>
                    </div>
                </div>

                <div class="prizes-section" data-aos="fade-left">
                    <h3 class="prizes-title">
                        <i class="fas fa-gift"></i>
                        Giải Thưởng Hấp Dẫn
                    </h3>

                    <div class="prizes-grid">
                        <div class="prize-card top1" data-aos="zoom-in" data-aos-delay="100">
                            <div class="prize-rank">
                                <span class="rank-number">1</span>
                                <span class="rank-text">TOP</span>
                            </div>
                            <div class="prize-badge">
                                <img src="../assets/images/badges/top1_cuoc_thi_moi.png" alt="Top 1 Badge" class="badge-image">
                                <div class="badge-glow"></div>
                            </div>
                            <div class="prize-details">
                                <h4>Huy Hiệu Độc Quyền</h4>
                                <div class="prize-value">150,000 VNĐ</div>
                                <p>Phần thưởng tiền mặt</p>
                            </div>
                        </div>

                        <div class="prize-card top2" data-aos="zoom-in" data-aos-delay="200">
                            <div class="prize-rank">
                                <span class="rank-number">2</span>
                                <span class="rank-text">TOP</span>
                            </div>
                            <div class="prize-badge">
                                <img src="../assets/images/badges/top2_cuoc_thi_moi.png" alt="Top 2 Badge" class="badge-image">
                                <div class="badge-glow"></div>
                            </div>
                            <div class="prize-details">
                                <h4>Huy Hiệu Độc Quyền</h4>
                                <div class="prize-value">100,000 VNĐ</div>
                                <p>Phần thưởng tiền mặt</p>
                            </div>
                        </div>

                        <div class="prize-card top3" data-aos="zoom-in" data-aos-delay="300">
                            <div class="prize-rank">
                                <span class="rank-number">3</span>
                                <span class="rank-text">TOP</span>
                            </div>
                            <div class="prize-badge">
                                <img src="../assets/images/badges/top3_cuoc_thi_moi.png" alt="Top 3 Badge" class="badge-image">
                                <div class="badge-glow"></div>
                            </div>
                            <div class="prize-details">
                                <h4>Huy Hiệu Độc Quyền</h4>
                                <div class="prize-value">50,000 VNĐ</div>
                                <p>Phần thưởng tiền mặt</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="event-cta" data-aos="fade-up" data-aos-delay="400">
                <a href="../rankings/" class="btn btn-event">
                    <i class="fas fa-chart-line"></i>
                    Xem Bảng Xếp Hạng
                </a>
                <a href="../classes/" class="btn btn-secondary">
                    <i class="fas fa-code"></i>
                    Tham Gia Ngay
                </a>
            </div>
        </div>

        <!-- Animated Background Elements -->
        <div class="event-bg-elements">
            <div class="bg-element trophy" style="--delay: 0s;">🏆</div>
            <div class="bg-element code" style="--delay: 1s;">💻</div>
            <div class="bg-element fire" style="--delay: 2s;">🔥</div>
            <div class="bg-element star" style="--delay: 0.5s;">⭐</div>
            <div class="bg-element rocket" style="--delay: 1.5s;">🚀</div>
        </div>

        <!-- Farm Code Challenge Event -->
        <div class="farm-event" data-aos="fade-up">
            <div class="event-header">
                <div class="event-badge" style="background: linear-gradient(135deg, #32CD32, #228B22);">
                    <i class="fas fa-seedling"></i>
                    <span>SẮP DIỄN RA</span>
                </div>
                <h2 class="event-title">
                    <span class="gradient-text">🌾 Farm Code Challenge 2026</span>
                    <div class="title-decoration">
                        <div class="decoration-line"></div>
                        <i class="fas fa-tractor"></i>
                        <div class="decoration-line"></div>
                    </div>
                </h2>
                <p class="event-subtitle">Cuộc thi lập trình điều khiển drone nông nghiệp thông minh</p>
            </div>

            <div class="farm-info-grid" data-aos="fade-up" data-aos-delay="200">
                <div class="farm-info-card">
                    <div class="info-header">
                        <i class="fas fa-calendar-alt"></i>
                        <h3>Thời Gian Dự Kiến</h3>
                    </div>
                    <p><strong>Năm 2026</strong><br>Thời gian cụ thể sẽ được thông báo sớm</p>
                </div>

                <div class="farm-info-card">
                    <div class="info-header">
                        <i class="fas fa-puzzle-piece"></i>
                        <h3>Hình Thức Tham Gia</h3>
                    </div>
                    <p>Sử dụng giao diện <strong>kéo thả trực quan</strong> để tạo thuật toán điều khiển drone</p>
                </div>

                <div class="farm-info-card">
                    <div class="info-header">
                        <i class="fas fa-brain"></i>
                        <h3>Kỹ Năng Cần Thiết</h3>
                    </div>
                    <p>Tư duy logic, hiểu về <strong>điều kiện</strong> và <strong>vòng lặp</strong></p>
                </div>
            </div>

            <div class="prizes-section" data-aos="fade-up" data-aos-delay="300">
                <h3 class="prizes-title">
                    <i class="fas fa-trophy"></i>
                    Giải Thưởng Đặc Biệt
                </h3>

                <div class="farm-prizes">
                    <div class="farm-prize-card top1">
                        <div class="prize-rank">
                            <span class="rank-number">1</span>
                            <span class="rank-text">TOP</span>
                        </div>
                        <div class="prize-badge">
                            <img src="../giainhat.png" alt="Bậc Thầy Nông Trại" class="badge-image">
                        </div>
                        <div class="prize-details">
                            <h4>Bậc Thầy Nông Trại</h4>
                            <div class="prize-value">200,000 VNĐ</div>
                            <p>Phần thưởng tiền mặt</p>
                        </div>
                    </div>

                    <div class="farm-prize-card top2">
                        <div class="prize-rank">
                            <span class="rank-number">2</span>
                            <span class="rank-text">TOP</span>
                        </div>
                        <div class="prize-badge">
                            <img src="../giainhi.png" alt="Chuyên Gia Tự Động Hóa" class="badge-image">
                        </div>
                        <div class="prize-details">
                            <h4>Chuyên Gia Tự Động Hóa</h4>
                            <div class="prize-value">150,000 VNĐ</div>
                            <p>Phần thưởng tiền mặt</p>
                        </div>
                    </div>

                    <div class="farm-prize-card top3">
                        <div class="prize-rank">
                            <span class="rank-number">3</span>
                            <span class="rank-text">TOP</span>
                        </div>
                        <div class="prize-badge">
                            <img src="../giaiba.png" alt="Lập Trình Viên Canh Tác" class="badge-image">
                        </div>
                        <div class="prize-details">
                            <h4>Lập Trình Viên Canh Tác</h4>
                            <div class="prize-value">100,000 VNĐ</div>
                            <p>Phần thưởng tiền mặt</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="event-cta" data-aos="fade-up" data-aos-delay="400">
                <a href="../tests/farm-code-demo.html" class="btn btn-event" style="background: linear-gradient(135deg, #32CD32, #228B22);">
                    <i class="fas fa-seedling"></i>
                    Xem Demo
                </a>
                <a href="../auth/register.html" class="btn btn-secondary">
                    <i class="fas fa-user-plus"></i>
                    Đăng Ký Tham Gia
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- AOS Animation Library -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 1000,
            once: true,
            offset: 100
        });
    </script>

    <script src="../assets/js/script.js"></script>
</body>
</html>
